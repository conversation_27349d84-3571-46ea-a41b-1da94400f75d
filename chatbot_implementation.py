import re
from pymilvus import (connections, Collection, utility)
from sentence_transformers import SentenceTransformer
import logging
from fuzzywuzzy import process
from nltk.corpus import words
from nltk.stem import WordNetLemmatizer
import spacy
from nltk.sentiment.vader import SentimentIntensityAnalyzer

# --- Setup logging ---
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Load NLP tools ---
try:
    nlp = spacy.load('en_core_web_sm')
except OSError:
    import subprocess
    import sys
    subprocess.run([sys.executable, "-m", "spacy", "download", "en_core_web_sm"])
    nlp = spacy.load('en_core_web_sm')

lemmatizer = WordNetLemmatizer()
word_list = set(words.words())
sentiment_analyzer = SentimentIntensityAnalyzer()
connections.connect(host="localhost", port="19530")

# --- Lazy loading for embedding model ---
_model = None

def get_model():
    """Lazy load the SentenceTransformer model only when needed"""
    global _model
    if _model is None:
        logger.info("Loading SentenceTransformer model (first time only)...")
        _model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")
        logger.info("SentenceTransformer model loaded successfully!")
    return _model

def get_collections():
    """
    Fetch available collections dynamically from Milvus.
    Returns a dictionary where the key is the user-friendly name,
    and the value is the actual collection name.
    """
    collections = utility.list_collections()  # Get all collection names
    collection_dict = {name: name for name in collections}  # Map names dynamically
    return collection_dict
print('collection_dict')
print('100.............')

# Load collections dynamically
COLLECTIONS = get_collections()

print("Available Collections:", COLLECTIONS)  # Corrected print statement
 
# Example usage: Fetching a collection dynamically
def get_collection_name(collection_id):
    """
    Get the collection name based on collection_id dynamically.
    """
    COLLECTIONS = get_collections()  # Refresh the list

    # If specific collection_id is requested, try to find it
    if collection_id in COLLECTIONS:
        return COLLECTIONS[collection_id]

    # If no specific collection found, return the first available collection
    if COLLECTIONS:
        first_collection = list(COLLECTIONS.values())[0]
        print(f"Using default collection: {first_collection}")
        return first_collection

    # If no collections available, return None
    print("No collections available in Milvus")
    return None


# --- Generate embeddings ---
def generate_embeddings(text, model):
    """Generate embeddings for the input text"""
    embedding = model.encode(text)
    return embedding


# --- Preprocess text ---
def preprocess_text(text):
    """Preprocess text for NLP tasks"""
    text = text.lower()
    doc = nlp(text)
    return ' '.join([token.lemma_ for token in doc])

# --- Check if text is gibberish ---
def is_gibberish_with_nltk(text):
    """Check if the input text is gibberish"""
    # Define a custom list of valid words for Silpasathi
    custom_valid_words = {
        "silpasathi", "sp", "silpasathi prakalap", "silpa", "sathi", "silpa sathi",
        "silpasathi prakalpa", "silpasathi portal", "silpa sathi portal", "silpa sathi prakalpa",
        "silpssathi", "silpassathi", "silpaasathi", "silpsathi", "silapasathi", "silpasati", 
        "silpasaathi", "silpsaathi", "silpaasathi", "silpasathiportal", "silpasathi prakalp",
        "silpa sathiportal", "silpa sathiprakalpa", "silpasathiprakalpa", "silpasathiportal", "fees", "timeline", "required timeline", "documents", "required documents", "payment required"
    }

    # Remove non-alphabetic characters from the text
    clean_text = re.sub(r'[^a-zA-Z\s]', '', text)
    words_in_text = clean_text.split()

    # If the input is empty or invalid, consider it gibberish
    if not words_in_text:
        return True

    # Count valid words from NLTK word list and custom list
    valid_words = sum(1 for word in words_in_text if word.lower() in word_list or word.lower() in custom_valid_words)
    total_words = len(words_in_text)

    # Calculate the ratio of valid words
    gibberish_ratio = valid_words / total_words if total_words > 0 else 0
    THRESHOLD = 0.5  # Lowered threshold to reduce false positives

    return gibberish_ratio < THRESHOLD

# --- Main chatbot response function ---
def chatbot_response(req, collection_id, followup_yes='', followup_no=''):
    """Main chatbot response function"""
    # Predefined responses for common greetings
    predefined_responses = {
        "hi": "Namaskar🙏🏻! How can I assist you today?",
        "hello": "Namaskar🙏🏻! How can I assist you today?",
        "thanks": "You're welcome! If you have any other questions, feel free to ask.",
        "thank you": "You're welcome! If you have any other questions, feel free to ask.",
        "who are you": "Hi, I am AI Sanlaap, a chatbot developed by CoE-AI Lab Kolkata, NIC WBSC."
    }

    # Handle single-word queries
    single_word_queries = {"how", "what", "why", "when", "where", "who", "about", "they", "them",
                          "this", "that", "in", "on", "are", "was", "have", "has", "should",
                          "had", "could", "i love", "love", "bad"}
    req_cleaned = re.sub(r'[^A-Za-z\s]', '', req.lower()).strip()

    if req_cleaned in single_word_queries:
        return {
            "id": '',
            "response": "Could you please provide more details? I'm here to assist you!",
            "followup_yes": '',
            "followup_no": '',
            "score": '',
            "intent_id": ''
        }

    # Check if input is gibberish
    if is_gibberish_with_nltk(req):
        logger.info(f"Query '{req}' detected as gibberish")
        return {
            "id": '',
            "intent_name": 'gibberish',
            'response': "Sorry, I apologize 😊, but please inquire if you need anything else. I'm glad to assist.",
            "followup_yes": '',
            "followup_no": '',
            "score": '',
            "intent_id": ''
        }

    req_cleaned = re.sub(r'[^A-Za-z\s]', '', req.lower()).strip()
    best_match, score = process.extractOne(req_cleaned, predefined_responses.keys())
    if score >= 90:
        # # 32 greetings intent insertion
        # chat_stat(operation='insert', values={'intent_id': 'greetings',
        #                                       'question': req.lower().strip() 
        #                                         })
        return {
            "id": '',
            "intent_name" :'greetings',
            "response": predefined_responses[best_match], 
            "followup_yes": '',
            "followup_no": '',
            "score": '',
            "intent_id":'104'
        }

    req_preprocessed = preprocess_text(req_cleaned)
    req_embeddings = generate_embeddings(req_preprocessed, get_model())


    # Check if collection_id is already a full collection name or needs to be resolved
    if collection_id.startswith("collection_"):
        # It's already a full collection name, use it directly
        collection_name = collection_id
    else:
        # It's a collection ID that needs to be resolved
        collection_name = get_collection_name(collection_id)

    if not collection_name:
        return {"response": f"Unknown collection_id: {collection_id}", "followup_intent": None}

    collection = Collection(collection_name)
    print(f"Loaded collection: {collection_name}")
    collection.load()
    
    results = collection.search(
            data=[req_embeddings.tolist()],
            anns_field="intent_related_text",
            param={"metric_type": "COSINE", "params": {"nlist": 128}},
            limit=10,
            output_fields=["id", "intent_name", "response", "followup_yes", "followup_no","intent_id"]
        )
   
    THRESHOLD = 0.75  # Lowered threshold to capture more relevant matches

    #adaptive_threshold = get_adaptive_threshold(collection_name)

   # if results and len(results[0]) > 0:
        #top_result = results[0][0]

        # Store the retrieved score in history
        # historical_scores[collection_name].append(top_result.score)
        #if len(historical_scores[collection_name]) > 100:  # Limit history size
          #  historical_scores[collection_name].pop(0)
    if results and len(results[0]) > 0:
        if followup_yes != '' or followup_no != '':
            pos_res_score = neg_res_score = 0
            for res in results[0]:
                print(res.entity.get("id"))
                print(res.score)
                if res.entity.get("intent_name") == followup_yes and pos_res_score == 0:
                    pos_res_score = res.score
                    print("pos sc",pos_res_score)
                    pos_res = res
                if res.entity.get("intent_name") == followup_no and neg_res_score == 0:
                    neg_res_score = res.score
                    neg_res = res
                    print("neg sc",neg_res_score)
            try:
                if pos_res_score > neg_res_score:
                    # intent insertion
                    #pos_res.entity.get("intent_name")
                    # chat_stat(operation='insert', values={'intent_id': pos_res.entity.get("intent_name"),
                    #                                       'question': req.lower().strip() 
                    #                                         })
                    return {
                        "id": pos_res.id,
                        "intent_name" : pos_res.entity.get("intent_name"),
                        "response": pos_res.entity.get("response"),
                        "followup_yes": pos_res.entity.get("followup_yes"),
                        "followup_no": pos_res.entity.get("followup_no"),
                        "score": pos_res_score,
                        "intent_id":pos_res.entity.get("intent_id")
                    }
                else:
                    # intent insertion
                    # chat_stat(operation='insert', values={'intent_id': neg_res.entity.get("intent_name"),
                    #                                       'question': req.lower().strip() 
                    #                                         })
                    return {
                        "id": neg_res.id,
                        "intent_name" : neg_res.entity.get("intent_name"),
                        "response": neg_res.entity.get("response"),
                        "followup_yes": neg_res.entity.get("followup_yes"),
                        "followup_no": neg_res.entity.get("followup_no"),
                        "score": neg_res_score,
                        "intent_id":neg_res.entity.get("intent_id")
                    }
            except:
                # intent insertion
                # chat_stat(operation='insert', values={'intent_id': results[0][0].entity.get("intent_name"),
                #                                       'question': req.lower().strip() 
                #                                         })
                return {
                        "id": results[0][0].id,
                        "intent_name" : results[0][0].entity.get("intent_name"),
                        "response": results[0][0].entity.get("response"),
                        "followup_yes": results[0][0].entity.get("followup_yes"),
                        "followup_no": results[0][0].entity.get("followup_no"),
                        "score": results[0][0].score,
                        "intent_id":results[0][0].entity.get("intent_id")
                    }

        else:
            top_result = results[0][0]
            print(f"Top result: {top_result}")
            print(f"Score: {top_result.score}, Threshold: {THRESHOLD}")
            print(f"Intent: {top_result.entity.get('intent_name')}, Response: {top_result.entity.get('response')}")

            if top_result.score >= THRESHOLD and top_result.entity.get("followup_yes") != ''  and top_result.entity.get("followup_no") != '':
                # Check follow-up intent fields
                followup_yes = top_result.entity.get("followup_yes")
                followup_no = top_result.entity.get("followup_no")
                print(f'Knowledge base match with followup - Score: {top_result.score}')
                return {
                    "id": top_result.id,
                    "intent_name" :top_result.entity.get("intent_name"),
                    "response": top_result.entity.get("response"),
                    "followup_yes": followup_yes,
                    "followup_no": followup_no,
                    "score": top_result.score,
                    "intent_id":top_result.entity.get("intent_id")
                }
            elif top_result.score >= THRESHOLD:
                followup_yes = top_result.entity.get("followup_yes")
                followup_no = top_result.entity.get("followup_no")
                print(f'Knowledge base match - Score: {top_result.score}')
                return {
                    "id": top_result.id,
                    "intent_name" :top_result.entity.get("intent_name"),
                    "response": top_result.entity.get("response"),
                    "followup_yes": followup_yes,
                    "followup_no": followup_no,
                    "score": top_result.score,
                    "intent_id":top_result.entity.get("intent_id")
                }
            # else:
            #     req_cleaned = re.sub(r'[^A-Za-z\s]', '', req.lower()).strip()
            #     best_match, score = process.extractOne(req_cleaned, predefined_responses.keys())
            #     if score >= 85:
            #         return {
            #             "id": '',
            #             'response': predefined_responses[best_match],
            #             "followup_yes": '',
            #             "followup_no": '',
            #             "score": '',
            #             "intent_id": ''
            #         }
                # intent insertion
                # chat_stat(operation='insert', values={'intent_id': 'fallback',
                #                                       'question': req.lower().strip() 
                #  
                #                                        })
            else:
                print(f'Going to fallback - Score: {top_result.score} < Threshold: {THRESHOLD}')
                return {
                        "id": '',
                        "intent_name" :'fallback',
                        "response": "Thank you for your query! Could you please clarify or provide more details so that I can assist you effectively?",
                        "followup_yes": '',
                        "followup_no": '',
                        "score": '',
                        "intent_id":'103'
                    }
    # If no relevant match is found
    else:    
        # intent insertion
        # chat_stat(operation='insert', values={'intent_id': 'fallback',
        #                                       'question': req.lower().strip() 
        #                                         })
        return {
            "id": '',
            "intent_name" :'fallback',  
            "response": "Thank you for your query! Could you please clarify or provide more details so that I can assist you effectively?",
            "followup_yes": '',
            "followup_no": '',
            "score": '',
            "intent_id":'103'
        }
