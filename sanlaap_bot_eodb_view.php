<style>
  .options-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    /* center buttons horizontally */
    gap: 10px;
    /* space between buttons */
    margin-top: 10px;
  }

  .option_btn {
    width: auto;
    min-width: 200px;
    max-width: 500px;
    padding: 8px 12px;
    font-size: 11px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    /*background: linear-gradient(145deg, #6e7bff, #4f5bff);*/
    background: linear-gradient(65deg, #24bcedc4,rgba(20, 158, 149, 0.4)); 
    color: black;
    font-weight: bold;
    text-align: center;
    white-space: normal;
    word-wrap: break-word;
    line-height: 1.4;
    height: auto;
    overflow-wrap: break-word;
  }

  .option_btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
  }

  .option_btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .option_btn.yes_btn {
    background: linear-gradient(145deg, #56e39f, #2c9f4b);
  }

  .option_btn.no_btn {
    background: linear-gradient(145deg, #f76c6c, #d32f2f);
  }



</style>
<style>
  .dislike {
    font-size: 20px;
    outline: none;
    color: #0080df;
  }

  .chatbox__image--header {
    width: 11%;
    height: 100%;
    border: 1px solid #000;
    background: #f5eb5c;
    border-radius: 50%;
    margin-right: 10px;
  }

  .chatbox__image--header img {
    width: 100%;
  }

  .chatbot {
    width: 10%;
  }

  .chatbot img {
    width: 62px;
    background: #4089e7;
    float: right;
    border-radius: 50%;
    height: 62px;
    object-fit: contain;
    position: fixed;
    right: 79px;
    z-index: 99999;
    bottom: 60px;
    border: 2px solid #007b95;
  }

  .dislike-btn {
    transform: scaleX(-1);
  }

  .like {
    font-size: 20px;
    outline: none;
    color: #0080df;
  }

  .like-pop {
    font-size: 45px;
    outline: none;
    color: #0080df;
  }

  .chatbox__messages {
    height: 400px;
    overflow-y: auto;
  }

  .messages__parent {
    position: relative;
    padding-bottom: 30px;
  }

  .messages__item--loader {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: center;
    color: #007bff;
  }

  .messages__item--loader .dot {
    width: 8px;
    height: 8px;
    background-color: #333;
    border-radius: 50%;
    margin: 0 3px;
    animation: dot-blink 1s infinite ease-in-out;
    color: #007bff;
  }

  @keyframes dot-blink {
    0% {
      opacity: 0;
    }

    50% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  /*************chatbot*************** */

  .messages__item--loader {
    display: flex;
    justify-content: center;
    align-items: center;
    height: auto;
    margin: 10px 0;
  }

  .messages__item--loader .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: gray;
    animation: blink 1.4s infinite;
  }

  .messages__item--loader .dot:nth-child(2) {
    animation-delay: 0.2s;
  }

  .messages__item--loader .dot:nth-child(3) {
    animation-delay: 0.4s;
  }

  @keyframes blink {

    0%,
    80%,
    100% {
      opacity: 0;
    }

    40% {
      opacity: 1;
    }
  }

  .loader {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    margin: 10px 0;
  }

  .dot {
    width: 8px;
    height: 8px;
    background-color: #007bff;
    border-radius: 50%;
    animation: bounce 1.5s infinite ease-in-out;
  }

  .dot:nth-child(2) {
    animation-delay: 0.3s;
  }

  .dot:nth-child(3) {
    animation-delay: 0.6s;
  }

  @keyframes bounce {

    0%,
    80%,
    100% {
      transform: scale(0);
      opacity: 0.5;
    }

    40% {
      transform: scale(1);
      opacity: 1;
    }
  }

  .lng-box {
    background-color: #f1f1f1;
    padding: 10px;
    border-radius: 5px;
    position: absolute;
    top: 30%;
    left: 0;
    right: 0;
    width: 300px;
    margin: 0 auto;
    text-align: center;
  }

  .language-selection {
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: #0008;
    height: 100vh;
    z-index: 99999999999999;
  }

  #language {
    margin-bottom: 5px;
  }

  .messages__icon {
    margin-right: 10px;
  }

  .chatbox__support {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .messages__parent {
    width: 343px;
  }

  .chatbox__messages {
    flex-grow: 1;
    overflow-y: auto;
    padding: 10px;
    border-bottom: 1px solid #ccc;
  }

  .main-suggestions {
    background-color: #f9f9f9;
    padding: 10px;
    border-top: 1px solid #ccc;
    max-height: 100px;
    overflow-y: auto;
    width: 100%;
  }

  .chatbox__footer {
    padding: 10px;
    display: flex;
    align-items: center;
  }



    .disabled {
    pointer-events: none;
    opacity: 0.5;
    cursor: not-allowed;
  }

  .suggestion-item {
    border-radius: 20px;
    text-align: left;
    overflow-wrap: break-word;
    background: linear-gradient(65deg, #33b1be80, #32c5c01a) !important;
    color: #000;
    font-weight: 550;
    border: 1px solid rgb(7 203 223);
    min-height: 38px;
    font-size: 12px;
    padding: 4px 10px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .suggestion-item:hover {
    background-color: #d4d4d4;
  }

  .suggestion-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f0f0f0 !important;
    color: #999 !important;
  }

  .suggestion-item.disabled:hover {
    background: #f0f0f0 !important;
  }

  .messages__item--visitor {
    float: right;
    width: auto;
  }

  .messages__wrapper {
    width: 100%;
    display: inline-block;
  }

  .messages__item--operator {
    float: left;
  }

  /*chatbox*/
  .chatbox {
    position: fixed;
    right: 0;
    bottom: 5px;
    max-width: 400px;
    width: 100%;
    border-radius: 22px;
    overflow: hidden;
    box-shadow: rgba(50, 50, 93, 0.25) 0px 30px 60px -12px, rgba(0, 0, 0, 0.3) 0px 18px 36px -18px;
    z-index: 99999;
    display: none;
  }

  .chatbox__support {
    background: #f9f9f9;
    height: 600px;
    width: 100%;
    box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    overflow: hidden;
    transition: all 0.5s ease-in-out;
    transform: translateY(0);
    opacity: 1;
  }

  .chatbox--active .chatbox__support {
    transform: translateY(-40px);
    z-index: 123456;
    opacity: 1;
  }

  .chatbox__header {
    background: linear-gradient(65deg, #42d2e1, #32c5c01a) !important;
    /* background: linear-gradient(52deg, #003156 0.52%, #006262 100%); */
    /* background:linear-gradient(93.12deg, #054a95 0.52%, #0452a7 100%); */
    /*background: linear-gradient(93.12deg, #581b98 0.52%, #9c1de7 100%);*/
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px 20px;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    color: #000;
  }

  .chatbox__image--header img {
    margin-right: 10px;
    border-radius: 50%;
  }

  .chatbox__heading--header {
    font-size: 1.2rem;
    color: #000;
    margin: 0;
  }

  .chatbox__description--header {
    font-size: 0.9rem;
    color: #000;
    margin: 0;
  }

  .chatbox__messages {
    padding: 20px;
    overflow-y: auto;
    height: calc(100% - 142px);
    background-image: url('assets/site_resources/images/e-nathikaran/silver-abstract-background.jpg');
    background-size: cover;
  }

  .messages__item {
    margin-bottom: 10px;
    padding: 10px;
    border-radius: 10px;
    max-width: 70%;
    word-wrap: break-word;
  }

  .messages__item--visitor {
    background-color: #4dc6da;
    align-self: flex-start;
    color: #000000;
    font-size: 13px;
    font-weight: 600;
    background: linear-gradient(65deg, #24bcedc4, #64a5a166) !important;
    border-top-left-radius: 20px;
    border-bottom-right-radius: 20px;
    border-bottom-left-radius: 20px;
  }

  .messages__item--operator {
    background-color: #fff;
    color: #000;
    align-self: flex-end;
    font-size: 13px;
    box-shadow: 2px 3px 12px #00000040;
    text-align: justify;
  }

  .chatbox__footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px;
    /*background: linear-gradient(268.91deg, #581b98 -2.14%, #9c1de7 99.69%);*/
    /* background: linear-gradient(268.91deg, #054a95 -2.14%, #0452a7 99.69%); */
    /* background: linear-gradient(52deg, #003156 0.52%, #006262 100%); */
    background: linear-gradient(52deg, #5494c5 0.52%, #1f7878 100%);
    box-shadow: 0px -10px 15px rgba(0, 0, 0, 0.1);
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    background-color: #fff;
  }

  .chatbox__footer input[type="text"] {
    flex: 1;
    border: none;
    padding: 10px;
    border-radius: 30px;
    outline: none;
    font-size: 14px;
    margin-right: 3px;
    border: 1px solid #0002;
  }

  .chatbox__send--footer {
    background-color: white;
    color: var(--primary);
    padding: 10px 20px;
    border-radius: 50px;
    border: none;
    outline: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .chatbox__send--footer:hover {
    background-color: #f0f0f0;
  }

  .chatbox__button {
    position: absolute;
    bottom: 20px;
    right: 20px;
    z-index: 999999;
  }

  .chatbox__button button {
    background-color: transparent;
    border: none;
    outline: none;
    cursor: pointer;
  }

  .close {
    cursor: pointer;
  }

  .btn-whatsapp-pulse .fa {
    font-size: 20px;
  }

  .btn-whatsapp-pulse {
    /*background: #016ecd;*/
    color: white;
    position: fixed;
    bottom: 61px;
    right: 80px;
    /* font-size: 142px; */
    display: flex;
    justify-content: center;
    align-items: center;
    width: 0;
    height: 0;
    padding: 30px;
    text-decoration: none;
    border-radius: 50%;
    animation-name: pulse;
    animation-duration: 1.5s;
    animation-timing-function: ease-out;
    animation-iteration-count: infinite;
    border: 1px solid #a2a2a2;
  }

  .btn-whatsapp-pulse:hover {
    color: #ecc233;
  }

  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.5);
    }

    80% {
      box-shadow: 0 0 0 14px rgba(37, 211, 102, 0);
    }
  }

  .btn-whatsapp-pulse-border {
    bottom: 120px;
    right: 20px;
    animation-play-state: paused;
  }

  .btn-whatsapp-pulse-border::before {
    content: "";
    position: absolute;
    border-radius: 50%;
    padding: 25px;
    border: 5px solid #25d366;
    opacity: 0.75;
    animation-name: pulse-border;
    animation-duration: 1.5s;
    animation-timing-function: ease-out;
    animation-iteration-count: infinite;
  }

  @keyframes pulse-border {
    0% {
      padding: 25px;
      opacity: 0.75;
    }

    75% {
      padding: 50px;
      opacity: 0;
    }

    100% {
      opacity: 0;
    }
  }

  /*chatbox-end*/
  .default-query-dropdown {
    color: white;
    text-align: left;
    width: 100%;
    position: relative;
    z-index: 10;
    text-align: center;
  }

  /* Dropdown Button */
  .default-query-button {
    background-color: #ecbefa;
    color: white;
    border: none;
    padding: 0px;
    width: 100%;
    font-size: 10px;
    text-align: left;
    cursor: pointer;
  }

  .default-query-button:hover {
    background-color: #004494;
  }

  /* Dropdown Content */
  .dropdown-content {
    display: none;
    position: absolute;
    left: 0;
    width: 100%;
    background-color: white;
    border: 1px solid #ddd;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 20;
  }

  .dropdown-content button {
    border-radius: 20px;
    text-align: center;
    overflow-wrap: break-word;
    background: linear-gradient(65deg, #33b1be80, #32c5c01a) !important;
    color: #333;
    font-weight: 550;
    border: 1px solid rgb(7 203 223);
    min-height: 38px;
    white-space: break-spaces;
    font-size: 12px;
    padding: 4px 10px;
    background-color: white;
    color: black;
    padding: 10px;
    border: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    font-size: 14px;
  }

  .default_suggestion {
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 0px 20px 20px 20px;
    box-shadow: 0 1px 3px 0 #cdcdcd !important;
    margin: 10px;
  }

  .dropdown-content button:hover {
    background-color: #f1f1f1;
    /* Highlighted on hover */
  }

  /* Show dropdown on hover */
  .default-query-dropdown:hover .dropdown-content {
    display: block;
  }

  .e-nathikaran img {
    transition: 0.5s ease-in-out;
  }

  .e-nathikaran img:hover {
    transform: translateY(-5px);
  }

  /*reaction*/

  .messages__wrapper {
    margin-bottom: 10px;
  }

  .messages__item {
    padding: 10px;
    border-radius: 12px 12px 0px 12px;
    background-color: #f1f1f1;
    position: relative;
  }

  .messages__item--sender {
    background-color: #d0eaff;
  }

  .messages__item--operator {
    background-color: #fff;
    /* font-style: italic;*/
    border-radius: 0px 12px 12px 12px;
  }

  .operator-icon {
    position: absolute;
    left: -30px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 20px;
  }

  .reactions-container {
    margin-top: 5px;
  }

  .like-btn,
  .dislike-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: #555;
  }

  .like-btn:hover,
  .dislike-btn:hover {
    color: #007bff;
  }

  /* Modal styles */
  .reaction-modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    overflow: auto;
    padding-top: 60px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  }

  .reaction-modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    border-radius: 20px;
  }

  .close-btn {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
  }

  .close-btn:hover,
  .close-btn:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
  }

  .chat-form label {
    font-weight: bold;
    font-size: 12px;
  }

  .chat-form input {
    font-size: 12px;
  }

  .chat-form .form-group {
    margin: 3px 0;
    margin-top: 5px;
  }
</style>
<!-- jQuery UI CSS -->
<link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">

<!-- jQuery UI and jQuery JS -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<?php
$this->load->view("templates/header_view2");
?>
<a href="javascript:void(0)" class="chatbot btn-whatsapp-pulse">
  <img src="<?php echo base_url('assets/site_resources/images/chat.png'); ?>" alt="Avatar">
</a>
<!-- Language Selection Section -->
<div class="language-selection" style="display: none;">
  <div class="lng-box">
    <label for="language">Select Language:</label>
    <select id="language" class="form-control">
      <option value="1">English</option>
      <option value="2">Bengali</option>
    </select>
    <button class="btn-select-language btn btn-primary" id="select_L_B">Select</button>
  </div>
</div>
<div class="chatbox">
  <div class="chatbox__support">
    <div class="chatbox__header">
      <div class="d-flex" style="align-items:center">
        <div class="chatbox__image--header">
          <img src="<?php echo base_url('assets/site_resources/images/chat.png'); ?>" alt="Avatar">
        </div>
        <div class="chatbox__content--header">
          <h4 class="chatbox__heading--header">AI Sanlaap(EoDB)</h4>
          <p class="chatbox__description--header">Hi,How can I help you?</p>
        </div>
      </div>
      <div class="close">
        <i class="fa fa-times" aria-hidden="true"></i>
      </div>
    </div>
    <!-- Slim Dropdown for Default Query -->
    <div class="default-query-dropdown">
    </div>
    <div class="chatbox__messages" id="chat-messages">
      <div id="default_suggestion" class="default_suggestion">
        <p style="font-size: 11px; width: 100%; color: #000; font-weight: 400;margin-bottom: 10px;text-align:left"><b>Namaskar ! I am
            your Virtual Assistant for AI Sanlaap (EoDB) !
            <p style="font-size: 11px; width: 100%; color: #000; font-weight: 400;margin-bottom: 10px;white-space: normal;text-align:left">
              I can help you with the following services:
          </b></p>

        <div class="suggestion-item" onclick="selectQuestion('1. Apply for licence/clearance')" id="apply_licence">1. Apply for licence/clearance</div>
        <div class="suggestion-item" onclick="selectQuestion('2. Know application status')" id="know_status">2. Know application status</div>
        <div class="suggestion-item" onclick="selectQuestion('3. Raise any query / Grievance')" id="raise_query">3. Raise any query / Grievance</div>
        <div class="suggestion-item" onclick="selectQuestion('4. FAQ')" id="faq">4. FAQ</div>
        <p style="font-size: 11px; width: 100%; color: #000; font-weight: 400;margin-bottom: 10px;white-space: normal;text-align:left"><b>
            If you have any other queries, you may type here ........
          </b></p>
      </div>

      <div id="reactionModalD" class="reaction-modal">
        <div class="reaction-modal-content text-center">
          <!--   <span class="close-btn">&times;</span> -->
          <input type="hidden" id="dataR">
          <h3 style="font-weight:bold;">Sorry for your inconvenience.</h3>
          <p style="text-align: center;">Please share your feedback below. It will help me to improve myself.</p>
          <textarea id="feedback" name="feedback" class="form-control" style="margin:7px" placeholder="Enter your feedback ...."></textarea>
          <button id="reaction_submit" class="btn btn-primary mt-2 btn-sm" onclick="reactionSubmit()">Submit</button>
          <button class="btn btn-danger mt-2 btn-sm" onclick="cancel()">Cancel</button>
        </div>
      </div>
      <div id="reactionModal" class="reaction-modal">
        <div class="reaction-modal-content text-center">
          <h1><i class="fa-solid fa-thumbs-up like-pop"></i></h1>
        </div>
      </div>

      <span id="c_loader" style="display:none;"><img src="<?php echo base_url('assets/site_resources/images/Animation.gif'); ?>" style="  width: 80px;
          position: absolute;
          bottom: 40px;
        left: 22px;" alt=""></span>
    </div>
    <!-- Suggestions container moved outside of chatbox__messages -->
    <div class="suggestions">
      <!-- Suggested questions will go here -->
    </div>
    <div class="chatbox__footer">
      <a href="">
        <img id="lang" style="width:35px;margin-right:8px" src="<?php base_url() ?>assets/site_resources/images/hindi.png" alt="">
      </a>

      <input type="text" id="question" placeholder="Ask a question..." autocomplete="off">
      <a href="" style="margin:0px 5px;font-size:20px;color:#ffa700"><i class="fa fa-microphone" aria-hidden="true"></i></a>
      <button class="chatbox__send--footer" onclick="askQuestion()" id="send_button">
        <svg width="57px" height="54px" viewBox="1496 193 57 54" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 18px; height: 18px;">
          <g id="Group-9-Copy-3" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" transform="translate(1523.000000, 220.000000) rotate(-270.000000) translate(-1523.000000, -220.000000) translate(1499.000000, 193.000000)">
            <path d="M5.42994667,44.5306122 L16.5955554,44.5306122 L21.049938,20.423658 C21.6518463,17.1661523 26.3121212,17.1441362 26.9447801,20.3958097 L31.6405465,44.5306122 L42.5313185,44.5306122 L23.9806326,7.0871633 L5.42994667,44.5306122 Z M22.0420732,48.0757124 C21.779222,49.4982538 20.5386331,50.5306122 19.0920112,50.5306122 L1.59009899,50.5306122 C-1.20169244,50.5306122 -2.87079654,47.7697069 -1.64625638,45.2980459 L20.8461928,-0.101616237 C22.1967178,-2.8275701 25.7710778,-2.81438868 27.1150723,-0.101616237 L49.6075215,45.2980459 C50.8414042,47.7885641 49.1422456,50.5306122 46.3613062,50.5306122 L29.1679835,50.5306122 C27.7320366,50.5306122 26.4974445,49.5130766 26.2232033,48.1035608 L24.0760553,37.0678766 L22.0420732,48.0757124 Z" id="sendicon" fill="#96AAB4" fill-rule="nonzero"></path>
          </g>
        </svg>
      </button>
    </div>
  </div>
</div>
<script>
  // EODB Chatbot Configuration - Now managed by backend
  const EODB_CONFIG = <?php echo json_encode(isset($chatbot_config) ? $chatbot_config : array()); ?>;

  // Ensure backward compatibility with existing structure
  if (!EODB_CONFIG.api_endpoints) {
    EODB_CONFIG.api_endpoints = {};
  }

  // Set API endpoints from backend configuration or defaults
  EODB_CONFIG.sessionCreateUrl = EODB_CONFIG.api_endpoints.session_create_url || "<?php echo base_url('super_admin/ChatbotEntryController/sessionCreateEODB'); ?>";
  EODB_CONFIG.questionAnswerUrl = "<?php echo base_url('aiSanlaapBotQuestionAnswerEODB'); ?>";
  EODB_CONFIG.pythonApiUrl = "<?php echo base_url('super_admin/ChatbotEntryController/pythonApiProxy'); ?>"; // Use controller proxy
  EODB_CONFIG.pythonSessionCreateUrl = "<?php echo base_url('super_admin/ChatbotEntryController/createSessionWithWelcome'); ?>"; // Use controller method
  EODB_CONFIG.feedbackUrl = EODB_CONFIG.api_endpoints.feedback_url || "<?php echo base_url('super_admin/ChatbotEntryController/chatFeedbackEODB'); ?>";
  EODB_CONFIG.formSubmitUrl = EODB_CONFIG.api_endpoints.form_submit_url || "<?php echo base_url('super_admin/ChatbotEntryController/chatFormSubmitEODB'); ?>";

  // Set collection name
  EODB_CONFIG.collectionName = EODB_CONFIG.collection_name || "collection_b5e7c017_1a06_4857_819c_6a038133dd94";

  // Set keywords from backend or defaults - EXACT MATCH ONLY
  EODB_CONFIG.applicationStatusKeywords = (EODB_CONFIG.keywords && EODB_CONFIG.keywords.application_status) || ['application status', 'status'];
  EODB_CONFIG.licenceKeywords = (EODB_CONFIG.keywords && EODB_CONFIG.keywords.licence) || ['licence', 'clearance', 'apply'];
  EODB_CONFIG.exitKeywords = (EODB_CONFIG.keywords && EODB_CONFIG.keywords.exit) || ['exit', 'quit', 'restart', 'reset'];

  // Flow configuration
  EODB_CONFIG.skipSectorForStatus = true; // Skip sector/investment selection for application status
  EODB_CONFIG.enableGeneralQueryAtStart = true; // Enable general query textbox at initial greeting

  console.log("EODB_CONFIG loaded:", EODB_CONFIG);

  const lenis = new Lenis()
  lenis.on('scroll', (e) => {
    console.log(e)
  })

  function raf(time) {
    lenis.raf(time)
    requestAnimationFrame(raf)
  }
  requestAnimationFrame(raf)
</script>

<script>
  // Chatbot open functionality
  $('.btn-whatsapp-pulse').click(function() {
    // Enable send section from the start to allow general queries
    if (EODB_CONFIG.enableGeneralQueryAtStart) {
      enabledSendSection();
    } else {
      disabledSendSection();
    }
    $('.chatbox').css('display', 'block');
    //Request for Session id and welcome message
    $.ajax({
      url: EODB_CONFIG.pythonSessionCreateUrl,
      type: "POST",
      dataType: "json",
      success: function(data) {
        console.log("Session created with welcome:", JSON.stringify(data));
        currentSessionId = data.session_id;
        // Display welcome message from backend
        if (data.response) {
          addMessageToChatbox(data.response, "operator");
        }
      },
      error: function(error) {
        console.error("Error creating session:", error);
        handleBackendError(error);
      }
    });
  });
  $('.fa-times').click(function() {
    $('.chatbox').css('display', 'none');
  });

  function debounce(func, delay) {
    let timeout;
    return function(...args) {
      console.log("args=" + JSON.stringify(args));
      const context = this;
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(context, args), delay);
    };
  }
  /***********chatbot********** */
  function toggleChatbox() {
    const chatbox = $('#chatbox');
    const chatLang = $('.chat-lang');
    const chaticon = $('.icon-chat');
    const isChatboxVisible = chatbox.is(':visible');
    chatbox.toggle(!isChatboxVisible);
    chatLang.toggle(!isChatboxVisible);
    chaticon.toggleClass('hidden');
  }

  function showChatb() {
    $('.chat-lang').hide();
    $('#chatbox').show();
  }
  $('#lang').click(function(event) {
    event.preventDefault(); // Prevent the default behavior
    $('.language-selection').show();
  });
  $(document).ready(function() {
    $("#c_loader").hide();
    $("#c_loader").css('display', 'none');
    // Trigger askQuestion on Enter key press when in the #question input field
    $("#question").on("keydown", function(e) {
      if (e.key === "Enter") { // Check if Enter key is pressed

        askQuestion();
      }
    });
  });
  let selectedLanguage = null; // Initialize variable to hold the selected language
  let currentSessionId = null; // Session ID for Python API
  let currentStep = 1; // Current step in the conversation flow
  $('.btn-whatsapp-pulse').click(function() {
    // Show the language selection section
    //$('.language-selection').css('display', 'block');
  });
  // Handle the language selection
  $('.btn-select-language').click(function() {
    // Get the selected language
    //const language = $('#language').val();
    const language = "1";
    // Hide the language selection section
    $('.language-selection').css('display', 'none');
    $('.chatbox').css('display', 'block');
    // Now pass the selected language to the askQuestion and fetchSuggestions functions
    $("#c_loader").hide();
    askQuestion(language);
    //fetchSuggestions(language);
  });
  // /************************/close-chatbot*******
  $('.close .fa-times').click(function() {
    $('.chatbox').css('display', 'none');
    //$("#chat-messages").remove();
    $("#chat-messages").children().not("#default_suggestion").remove();
    enabledSendSection();
  });

  function askQuestion(language) {

    $("#c_loader").show();
    console.log("askQuestion= " + $("#question").val());
    if (!$("#question").val()) {
      $("#c_loader").hide();
      return;
    }

    if (typeof language !== 'undefined') {
      selectedLanguage = language;
    }
    console.log("language=" + selectedLanguage);

    const question = $("#question").val().trim();
    if (question) {
      // Check for special keywords first
      if (checkSpecialKeywords(question)) {
        $("#question").val("");
        $("#c_loader").hide();
        return;
      }

      // Add user message to chatbox
      addMessageToChatbox(question, "visitor");

      // Use controller proxy for API communication
      $.ajax({
        url: EODB_CONFIG.pythonApiUrl,
        type: "POST",
        dataType: "json",
        contentType: "application/json",
        data: JSON.stringify({
          session_id: currentSessionId,
          user_input: question,
          step: currentStep || 1,
          response_type: "text",
          collection_name: EODB_CONFIG.collectionName
        }),
        success: function(data) {
          console.log("Python API Response:", JSON.stringify(data));
          handlePythonApiResponse(data);
          $("#c_loader").hide();
        },
        error: function(error) {
          console.error("Error:", error);
          handleBackendError(error);
          $("#c_loader").hide();
        }
      });
      $("#question").val("");
      $(".suggestions").empty();
    }
  }


  ///////////////////////////////////// AISanlaap /////////////////////////////////

  //options set 

  function addOptionMessageToChatbox(message, sender, dataId) {

    disabledSendSection();
    //23-6-25
    if(dataId==008){
      enabledSendSection();
    }
    else if(dataId==001){
      enabledSendSection();
    }
    else if(message.intent_id==122){
      enabledSendSection();
    }
    //const dataObj = JSON.parse(data);
    data = JSON.stringify(message)

    console.log("from addOptionMessageToChatbox");
    console.log("message" + message);
    console.log("message data=" + data);
    console.log("message data intent_id=" + message.intent_id);
    console.log("message session_id=" + message.session_id);
    console.log(message.option_list);
    console.log("length=" + message.option_list.length);
    console.log("criteria_list=" + message.criteria_list);

    /*for (var i = 0; i < message.option_list.length; i++) {

    }*/
    // Start with the form_div structure
    let form_div = `
      <div id="form_div" class="default_suggestion chat-form">
        <div class="form-group">
          <label>${message.response}</label>
        </div>   
      </div>
    `;

    // Add the buttons (if any) to the form_div
    if (message.option_list && message.option_list.length > 0) {
      let buttonHTML = message.option_list.join(''); // Join the array of button HTML strings
      console.log("buttonHTML=" + buttonHTML);
      form_div += `<div class="options-container">${buttonHTML}</div>`; // Append buttons inside a wrapper div
    }

    /*var form_div = `<div id="form_div" class="default_suggestion chat-form">
    
      <div class="form-group">
        <label>` + message.response + `</label>
      </div>   
  </div>`;*/
    scrollToBottom();
    console.log("chatbox data id is=" + dataId);
    const chatboxMessages = $(".chatbox__messages");
    // Create the outer wrapper div
    const outerDiv = $("<div>").addClass("messages__wrapper");
    // Create the message item div
    const messageItem = $("<div>")
      .addClass("messages__item")
      .addClass(`messages__item--${sender}`)
      .html(form_div);

    // Append the message item to the outer wrapper
    if (sender === "operator") {
      const likeButton = $("<button>")
        .addClass("like-btn")
        .html('<i class="fa-solid fa-thumbs-up like"></i>')
        .attr("data-id", dataId)
        .on("click", function() {
          openPopup("Liked!", dataId);
        });
      const dislikeButton = $("<button>")
        .addClass("dislike-btn")
        .html('<i class="fa-solid fa-thumbs-down dislike"></i>')
        .on("click", function() {
          openPopupD("Disliked!");
        });
      const reactionsContainer = $("<div>")
        .addClass("reactions-container")
        .append(likeButton)
        .append(dislikeButton);
      messageItem.append(reactionsContainer);
    }
    outerDiv.append(messageItem);

    chatboxMessages.append(outerDiv);
  }
  ///////////////////////////////////Add form to chat box/////////////////////////
  var count=0;
  function addFormToChatbox(message, sender, dataId) {
    disabledSendSection();
    console.log("from form box");
    count+=1;
    var form_div = ` 
        <div id="form_div${count}" class="default_suggestion chat-form">
        
          <div class="form-group">
            <label>${message.response}</label>
          </div>
          <div class="form-group">
            <label>DOB</label>
            <input type="date" class="form-control date${count}" name="dob" id="dob${count}" placeholder="Enter DOB">
          </div>
          <div class="form-group">
            <label>Gender</label>
            <select class="form-control gender${count}" name="gender" id="gender${count}">
              <option value="">Please Select</option>
              <option value="Male">Male</option>
              <option value="Female">Female</option>
              <option value="Other">Other</option>
            </select>
          </div>
          <div class="form-group">
            <label>Social Category</label>
            <select class="form-control social_category${count}" name="social_category" id="social_category${count}">
              <option value="">Please Select</option>
              <option value="General">General</option>
              <option value="SC">SC</option>
              <option value="ST">ST</option>
              <option value="OBC">OBC</option>
            </select>
          </div>
          <div class="form-group">
            <label>Marital Status</label>
            <select class="form-control marital_status${count}" name="marital_status" id="marital_status${count}">
              <option value="">Please Select</option>
              <option value="Unmarried">Unmarried</option>
              <option value="Married">Married</option>
              <option value="Widow">Widow</option>
            </select>
          </div>
          <div class="form-group">
            <label>Annual Income</label>
            <input type='number' class="form-control income${count}" name="income" id="income${count}"placeholder="Enter Annual income">
          </div>
          
          <div class="form-group text-center mt-4">
            <button class="btn btn-primary btn-sm" id="form_submit" name="form_submit"data-form-id=${count}>Submit</button>
          </div>
        
      </div>`;
    scrollToBottom();
    console.log("chatbox data id is=" + dataId);
    const chatboxMessages = $(".chatbox__messages");
    // Create the outer wrapper div
    const outerDiv = $("<div>").addClass("messages__wrapper");
    // Create the message item div
    const messageItem = $("<div>")
      .addClass("messages__item")
      .addClass(`messages__item--${sender}`)
      .html(form_div);

    

    // Append the message item to the outer wrapper
    if (sender === "operator") {
      const likeButton = $("<button>")
        .addClass("like-btn")
        .html('<i class="fa-solid fa-thumbs-up like"></i>')
        .attr("data-id", dataId)
        .on("click", function() {
          openPopup("Liked!", dataId);
        });
      const dislikeButton = $("<button>")
        .addClass("dislike-btn")
        .html('<i class="fa-solid fa-thumbs-down dislike"></i>')
        .on("click", function() {
          openPopupD("Disliked!");
        });
      const reactionsContainer = $("<div>")
        .addClass("reactions-container")
        .append(likeButton)
        .append(dislikeButton);
      messageItem.append(reactionsContainer);
    }
    outerDiv.append(messageItem);

    chatboxMessages.append(outerDiv);
  }

  //////////////////////////////////////Add Aadhaar From to Chat Box///////////////////////////////

  function disabledSendSection() {
    $("#question").prop("disabled", true);
    $("#send_button").prop("disabled", true);
  }

  function enabledSendSection() {
    $("#question").prop("disabled", false);
    $("#send_button").prop("disabled", false);
  }

  function addAadhaarFormToChatbox(message, sender, dataId) {

    console.log("from form box");
    //$('#form_div').css('display', 'block');
    //$('#form_div').removeAttr('hidden').show();

    disabledSendSection();

    var form_div = `<div id="aadhaar_form_div" class="default_suggestion chat-form">
    
      <div class="form-group">
        <label>Aadhaar Number</label>
        <input type="number" class="form-control" name="aadhaar_number" id="aadhaar_number" placeholder="Enter Aadhaar number" max="12">
      </div>
      <div class="form-group text-center mt-4">
        <button class="btn btn-primary btn-sm" id="aadhaar_form_submit" name="aadhaar_form_submit" >Submit</button>
      </div>
      
    
  </div>`;
    scrollToBottom();
    console.log("chatbox data id is=" + dataId);
    const chatboxMessages = $(".chatbox__messages");
    // Create the outer wrapper div
    const outerDiv = $("<div>").addClass("messages__wrapper");
    // Create the message item div
    const messageItem = $("<div>")
      .addClass("messages__item")
      .addClass(`messages__item--${sender}`)
      .html(form_div);
    // Append the message item to the outer wrapper
    if (sender === "operator") {
      const likeButton = $("<button>")
        .addClass("like-btn")
        .html('<i class="fa-solid fa-thumbs-up like"></i>')
        .attr("data-id", dataId)
        .on("click", function() {
          openPopup("Liked!", dataId);
        });
      const dislikeButton = $("<button>")
        .addClass("dislike-btn")
        .html('<i class="fa-solid fa-thumbs-down dislike"></i>')
        .on("click", function() {
          openPopupD("Disliked!");
        });
      const reactionsContainer = $("<div>")
        .addClass("reactions-container")
        .append(likeButton)
        .append(dislikeButton);
      messageItem.append(reactionsContainer);
    }
    outerDiv.append(messageItem);

    chatboxMessages.append(outerDiv);
  }

  function addMessageToChatbox(message, sender, dataId) {
    scrollToBottom();
    console.log("chatbox data id is=" + dataId);
    const chatboxMessages = $(".chatbox__messages");
    // Create the outer wrapper div
    const outerDiv = $("<div>").addClass("messages__wrapper");
    // Create the message item div
    const messageItem = $("<div>")
      .addClass("messages__item")
      .addClass(`messages__item--${sender}`)
      .html(message);
    // Append the message item to the outer wrapper
    if (sender === "operator") {
      const likeButton = $("<button>")
        .addClass("like-btn")
        .html('<i class="fa-solid fa-thumbs-up like"></i>')
        .attr("data-id", dataId)
        .on("click", function() {
          openPopup("Liked!", dataId);
        });
      const dislikeButton = $("<button>")
        .addClass("dislike-btn")
        .html('<i class="fa-solid fa-thumbs-down dislike"></i>')
        .on("click", function() {
          openPopupD("Disliked!");
        });
      const reactionsContainer = $("<div>")
        .addClass("reactions-container")
        .append(likeButton)
        .append(dislikeButton);
      messageItem.append(reactionsContainer);
    }
    outerDiv.append(messageItem);

    chatboxMessages.append(outerDiv);
  }

  function addMessageToChatboxWithButtons(message, sender, buttonContainer, dataId) {
    scrollToBottom();
    console.log("chatbox data id is=" + dataId);
    const chatboxMessages = $(".chatbox__messages");
    // Create the outer wrapper div
    const outerDiv = $("<div>").addClass("messages__wrapper");
    // Create the message item div with message and buttons combined
    const messageItem = $("<div>")
      .addClass("messages__item")
      .addClass(`messages__item--${sender}`)
      .html(message);

    // Add the buttons to the message
    if (buttonContainer) {
      messageItem.append(buttonContainer);
    }

    // Append the message item to the outer wrapper
    if (sender === "operator") {
      const likeButton = $("<button>")
        .addClass("like-btn")
        .html('<i class="fa-solid fa-thumbs-up like"></i>')
        .attr("data-id", dataId)
        .on("click", function() {
          openPopup("Liked!", dataId);
        });
      const dislikeButton = $("<button>")
        .addClass("dislike-btn")
        .html('<i class="fa-solid fa-thumbs-down dislike"></i>')
        .on("click", function() {
          openPopupD("Disliked!");
        });
      const reactionsContainer = $("<div>")
        .addClass("reactions-container")
        .append(likeButton)
        .append(dislikeButton);
      messageItem.append(reactionsContainer);
    }
    outerDiv.append(messageItem);

    chatboxMessages.append(outerDiv);
  }

  function openPopup(reaction, dataId) {
    const modal = $("#reactionModal");
    const reactionMessage = $("#reactionMessage");
    $("#feedback").val('');
    // Store the data-id in the modal as a data attribute
    $("#dataR").val(dataId);
    //alert($("#dataR").val());
    modal.data("data-id", dataId);
    // Set the message for the popup
    reactionMessage.text(`You clicked: ${reaction}`);

    // Show the modal
    modal.css("display", "block");
  }
  $(".close-btn").on("click", function() {
    count=0;
    const modal = $("#reactionModal");
    modal.css("display", "none");
  });

  function openPopupD(reaction) {
    const modal = $("#reactionModalD");
    modal.css("display", "block");
    setTimeout(function() {
      modal.css("display", "none");
    }, 3000);
  }
  $(".close-btn").on("click", function() {
    const modal = $("#reactionModalD");
    modal.css("display", "none");
  });
  // Function to close the popup modal
  $(window).on("click", function(event) {
    const modal = $("#reactionModal");
    if ($(event.target).is(modal)) {
      modal.css("display", "none");
    }
  });
  $(window).on("click", function(event) {
    const modal = $("#reactionModalD");
    if ($(event.target).is(modal)) {
      modal.css("display", "none");
    }
  });
  let loaderTimeout;

  function addLoader(parentDiv) {
    const loaderItem = $("<div>")
      .addClass("messages__item messages__item--loader")
      .html('<span class="dot"></span><span class="dot"></span><span class="dot"></span>');
    parentDiv.append(loaderItem);
    return loaderItem;
  }

  function removeLoader(loaderItem) {
    clearTimeout(loaderTimeout);
    loaderItem.remove();
  }

  /*function scrollToBottom() {
    const chatboxMessages = $(".chatbox__messages");
    console.log(chatboxMessages[0].scrollHeight); // Log the scroll height
    chatboxMessages.scrollTop(chatboxMessages[0].scrollHeight);
  }*/
  //25-04-25

  function scrollToBottom() {
    const chatboxMessages = $(".chatbox__messages");
    console.log("Initial scrollHeight:", chatboxMessages[0].scrollHeight);

    // Check if scrollHeight updates after content is added
    setTimeout(() => {
      console.log("After content addition - scrollHeight: ", chatboxMessages[0].scrollHeight);
      chatboxMessages.scrollTop(chatboxMessages[0].scrollHeight);
    }, 1); // Delay in case of content loading
  }

  let isScrolledToBottom = true;

  function checkStatus() {
    const applicant_id = $("#application_id").val();
    const year = $("#year").val();
    if ($.trim(applicant_id) === '' || $.trim(year) === '') {
      // Validation message now handled by backend
      return;
    }
    $.ajax({
      url: "/controller/status_method", // Replace with your CodeIgniter route for status checking
      type: "POST",
      dataType: "json",
      contentType: "application/json",
      data: JSON.stringify({
        applicant_id: applicant_id,
        year: year
      }),
      success: function(data) {
        $("#result").html(`<p>Status: ${data.status}</p>`);
      },
      error: function(error) {
        console.error("Error:", error);
        handleBackendError(error);
      }
    });
  }

  function selectQuestion(question) {
    // Disable all main menu options
    $('#apply_licence').addClass('disabled').off('click');
    $('#know_status').addClass('disabled').off('click');
    $('#raise_query').addClass('disabled').off('click');
    $('#faq').addClass('disabled').off('click');

    // Handle different question types
    if (question.includes('Know application status') || question.includes('application status')) {
      // For application status, skip sector/investment selection and go directly to CAF→OTP→status→query flow
      handleApplicationStatusFlow();
    } else if (question.includes('Apply for licence/clearance') || question.includes('licence') || question.includes('clearance')) {
      // For licence/clearance, go through normal flow
      handleLicenceFlow();
    } else {
      // For general queries, enable text input
      handleGeneralQuery(question);
    }
  }

  function handleApplicationStatusFlow() {
    // User message now handled by backend

    // Use controller proxy for application status flow
    $.ajax({
      url: EODB_CONFIG.pythonApiUrl,
      type: "POST",
      dataType: "json",
      contentType: "application/json",
      data: JSON.stringify({
        session_id: currentSessionId,
        step: 2,
        response_type: "options",
        collection_name: EODB_CONFIG.collectionName,
        user_response: {
          caption: "main_option",
          value: "2. Know application status"
        }
      }),
      success: function(data) {
        console.log("Application status flow response:", JSON.stringify(data));
        handlePythonApiResponse(data);
      },
      error: function(error) {
        console.error("Error:", error);
        handleBackendError(error);
      }
    });
  }

  function handleLicenceFlow() {
    // User message now handled by backend

    // Use controller proxy for licence/clearance flow
    // Set current step to 2 to get service types
    currentStep = 2;

    $.ajax({
      url: EODB_CONFIG.pythonApiUrl,
      type: "POST",
      dataType: "json",
      contentType: "application/json",
      data: JSON.stringify({
        session_id: currentSessionId,
        step: 2,
        response_type: "options",
        collection_name: EODB_CONFIG.collectionName,
        user_response: {
          caption: "main_option",
          value: "1. Apply for licence/clearance"
        }
      }),
      success: function(data) {
        console.log("Licence flow response:", JSON.stringify(data));
        handlePythonApiResponse(data);
      },
      error: function(error) {
        console.error("Error:", error);
        handleBackendError(error);
      }
    });
  }

  function handleGeneralQuery(question) {
    // Enable text input for general queries
    enabledSendSection();
    // Message now handled by backend
  }

  // Create session with welcome message via controller
  function createPythonApiSessionWithWelcome() {
    $.ajax({
      url: EODB_CONFIG.pythonSessionCreateUrl,
      type: "POST",
      dataType: "json",
      success: function(data) {
        console.log("Session created with welcome:", JSON.stringify(data));
        currentSessionId = data.session_id;
        // Display welcome message from backend
        if (data.response) {
          addMessageToChatbox(data.response, "operator");
        }
      },
      error: function(error) {
        console.error("Error creating session:", error);
        handleBackendError(error);
      }
    });
  }

  // Create Python API session (legacy function - kept for compatibility)
  function createPythonApiSession() {
    // Generate a simple session ID
    currentSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    console.log("Created Python API session:", currentSessionId);

    // Initialize with step 1 to get the main menu
    $.ajax({
      url: EODB_CONFIG.pythonApiUrl,
      type: "POST",
      dataType: "json",
      contentType: "application/json",
      data: JSON.stringify({
        session_id: currentSessionId,
        step: 1,
        response_type: "options",
        collection_name: EODB_CONFIG.collectionName
      }),
      success: function(data) {
        console.log("Python API session initialized:", JSON.stringify(data));
        // Don't show the response here as we'll show the welcome message instead
      },
      error: function(error) {
        console.error("Error creating Python API session:", error);
      }
    });
  }

  // Handle backend errors consistently
  function handleBackendError(error) {
    console.error("Backend error:", error);
    // Try to extract error message from response
    if (error.responseJSON && error.responseJSON.response) {
      addMessageToChatbox(error.responseJSON.response, "operator");
    } else if (error.responseText) {
      try {
        const errorData = JSON.parse(error.responseText);
        if (errorData.response) {
          addMessageToChatbox(errorData.response, "operator");
        }
      } catch (e) {
        // If parsing fails, backend should handle error messages
        console.error("Could not parse error response");
      }
    }
    // If no error message from backend, don't show anything - backend should handle all messages
  }

  // Handle Python API responses
  function handlePythonApiResponse(data) {
    console.log("Python API Response:", JSON.stringify(data));

    // Handle different response types
    if (data.response_type === "options" && data.option_list && data.option_list !== "NA") {
      // Show option buttons with response message
      if (Array.isArray(data.option_list)) {
        // Use response message exactly as provided by backend - no frontend formatting
        var responseMessage = data.response || "";

        // Create a container for the buttons
        var buttonContainer = $('<div class="options-container"></div>');

        data.option_list.forEach(function(option) {
          if (typeof option === 'string') {
            // Handle HTML buttons from Python API
            if (option.includes('<button')) {
              buttonContainer.append(option);
            } else {
              // Handle regular option text - create button
              buttonContainer.append(`<button type="button" class="option_btn" value="${option}">${option}</button>`);
            }
          }
        });

        // Add the combined message and buttons as one unit
        addMessageToChatboxWithButtons(responseMessage, "operator", buttonContainer);
      }
      disabledSendSection();
    } else if (data.response_type === "text") {
      // For text responses, add the message and check for buttons
      if (data.response) {
        addMessageToChatbox(data.response, "operator");
      }

      // Check if there are also buttons to display (for service query interface)
      if (data.option_list && data.option_list !== "NA" && Array.isArray(data.option_list) && data.option_list.length > 0) {
        // Create a container for the buttons
        var buttonContainer = $('<div class="options-container"></div>');

        data.option_list.forEach(function(option) {
          if (typeof option === 'string') {
            // Handle HTML buttons from Python API
            if (option.includes('<button')) {
              buttonContainer.append(option);
            } else {
              // Handle regular option text - create button
              buttonContainer.append(`<button type="button" class="option_btn" value="${option}">${option}</button>`);
            }
          }
        });

        // Add buttons to chatbox
        addMessageToChatboxWithButtons("", "operator", buttonContainer);
      }

      // Enable text input for user queries
      enabledSendSection();
    } else {
      // Default case - just add the response message
      if (data.response) {
        addMessageToChatbox(data.response, "operator");
      }
    }

    // Update current step
    if (data.step) {
      currentStep = data.step;
    }

    // Scroll to bottom
    $("#chat-messages").scrollTop($("#chat-messages")[0].scrollHeight);
  }

  // Handle option selection for Python API
  function selectPythonApiOption(option) {
    // Add user's selection to chatbox
    addMessageToChatbox(option, "visitor");

    // Disable send section while processing
    disabledSendSection();

    // Determine the appropriate caption and next step based on current step and option
    let caption = "selected_option";
    let nextStep = currentStep;
    let responseType = "options";

    console.log("Option selection - Current step:", currentStep, "Option:", option);

    if (option.includes("Pre-establishment") || option.includes("Pre-operational")) {
      caption = "service_type";
      nextStep = 3;
      console.log("Service type selected:", option);
    } else if (option.includes("Apply for licence/clearance")) {
      caption = "main_option";
      nextStep = 2;
      console.log("Main option selected:", option);
    } else if (option.includes("Know application status")) {
      caption = "main_option";
      nextStep = 2;
      console.log("Application status selected:", option);
    } else if (currentStep === 3) {
      // Service selection from Pre-establishment or Pre-operational list
      caption = "selected_service";
      nextStep = 4;
      responseType = "options"; // Request service query interface with buttons
      console.log("Service selected:", option);
    } else if (currentStep === 5) {
      // Service selection for application status (after OTP verification)
      caption = "service_selection";
      nextStep = 5; // Stay in step 5 for service status retrieval
      responseType = "options"; // Keep as options for proper service selection handling
      console.log("Application service selected:", option);
      console.log("Service selection - caption:", caption, "nextStep:", nextStep, "responseType:", responseType);
    } else if (currentStep === 4 && (option.includes("Required documents") || option.includes("Timeline") || option.includes("Fees"))) {
      // Service query buttons (Required documents, Timeline, Fees)
      caption = "service_query";
      nextStep = 4; // Stay in step 4 for service queries
      responseType = "text"; // Expect text response
      console.log("Service query button clicked:", option);
    } else {
      // Default handling for other options
      nextStep = currentStep + 1;
      console.log("Default handling - next step:", nextStep);
    }


    const requestData = {
      session_id: currentSessionId,
      step: nextStep,
      response_type: responseType,
      collection_name: EODB_CONFIG.collectionName,
      user_response: {
        caption: caption,
        value: option
      }
    };
    console.log("Sending to Python API:", JSON.stringify(requestData));

    $.ajax({
      url: EODB_CONFIG.pythonApiUrl,
      type: "POST",
      dataType: "json",
      contentType: "application/json",
      data: JSON.stringify(requestData),
      success: function(data) {
        console.log("Option selection response:", JSON.stringify(data));
        // Update current step based on response
        if (data.step) {
          currentStep = data.step;
          console.log("Updated current step to:", currentStep);
        }
        handlePythonApiResponse(data);
        $("#c_loader").hide();
      },
      error: function(error) {
        console.error("Error:", error);
        addMessageToChatbox(EODB_CONFIG.errorMessage, "operator");
        $("#c_loader").hide();
      }
    });
  }

  function showComingSoon(option) {
    // Coming soon message now handled by backend
    // Backend will provide appropriate response
  }

  // Handle chatbot responses consistently
  function handleChatbotResponse(data) {
    var response_type = data.response_type;
    var intent_id = data.intent_id;
    var resDataId = intent_id;

    setTimeout(function() {
      if (response_type == "form") {
        addFormToChatbox(data, "operator", resDataId);
      } else if (response_type == "options") {
        addOptionMessageToChatbox(data, "operator", resDataId);
      } else {
        addMessageToChatbox(data.response, "operator", resDataId);
      }
      $("#c_loader").hide();
    }, 500);
  }

  // Check for exit/restart keywords and redirect keywords
  function checkSpecialKeywords(input) {
    var lowerInput = input.toLowerCase().trim();

    // Exit/restart keywords
    if (EODB_CONFIG.exitKeywords.some(keyword => lowerInput === keyword)) {
      restartChatbot();
      return true;
    }

    // Exact word matching for application status keywords
    // Only trigger if the input exactly matches these specific phrases
    var exactApplicationStatusKeywords = ['application status', 'status'];
    if (exactApplicationStatusKeywords.some(keyword => lowerInput === keyword)) {
      handleApplicationStatusFlow();
      return true;
    }

    // Exact word matching for licence/clearance keywords
    // Only trigger if the input exactly matches these specific words
    var exactLicenceKeywords = ['licence', 'clearance', 'apply'];
    if (exactLicenceKeywords.some(keyword => lowerInput === keyword)) {
      handleLicenceFlow();
      return true;
    }

    return false;
  }

  // Restart chatbot function
  function restartChatbot() {
    // Clear chat messages except default suggestion
    $("#chat-messages").children().not("#default_suggestion").remove();

    // Reset session
    $.ajax({
      url: EODB_CONFIG.sessionCreateUrl,
      type: "POST",
      dataType: "json",
      data: {
        type: "session_reset"
      },
      success: function(data) {
        console.log("Session reset successfully");
        // Re-enable all main menu options
        $('#apply_licence').removeClass('disabled').on('click');
        $('#know_status').removeClass('disabled').on('click');
        $('#raise_query').removeClass('disabled').on('click');
        $('#faq').removeClass('disabled').on('click');

        // Enable send section for general queries
        if (EODB_CONFIG.enableGeneralQueryAtStart) {
          enabledSendSection();
        }

        // Restart message now handled by backend
      },
      error: function(error) {
        console.error("Error resetting session:", error);
      }
    });
  }

  function reactionSubmit() {
    dataid = $("#dataR").val();

    console.log($("#feedback").val());
    var feedback = $("#feedback").val();
    $("#reactionModal").hide();
    $.ajax({
      url: EODB_CONFIG.feedbackUrl,
      type: "POST",
      dataType: "json",
      data: {
        feedback: feedback,
        dataid: dataid
      },
      success: function(data) {
        console.log("Feedback response:", data);
        if (data.status === 'success') {
          // Feedback confirmation message now handled by backend
        }
      },
      error: function(error) {
        console.error("Error:", error);
        handleBackendError(error);
      }
    });
  }

  function cancel() {
    $("#reactionModal").hide();
  }

  $(document).ready(function() {

    // Use event delegation to handle the submit event
    //////////////////////////////////For from submit////////////////////////////
    $(document).on("click", "#form_submit", function(e) {
      c=$(this).data('form-id');
      //alert(c);

      isValidate = false;
      //console.log("hii");
      e.preventDefault();
      var dob = $("#dob"+c).val().trim();
      var gender = $("#gender"+c).val().trim();
      var marital_status = $("#marital_status"+c).val().trim();
      var social_category = $("#social_category"+c).val().trim();
      var income = $("#income"+c).val().trim();
      //var name = $("#name").val();
      /*var dob = $("#dob").val().trim();
      var gender = $("#gender").val().trim();
      var marital_status = $("#marital_status").val().trim();
      var social_category = $("#social_category").val().trim();
      var income = $("#income").val().trim();*/
      var ques="dob="+dob+", gender="+gender+", marital_status="+marital_status+", social_category="+social_category+", income="+income;
      addMessageToChatbox(ques, "visitor");
      /*if (name === "") {
        isValidate = false;
        $("#name_error").remove();
        $("#name").css("border-color", "red");
        $("#name").after('<span id="name_error" style="color: red; font-size: 12px;">Name Field is Required.</span>');
      }*/
      if (dob === "") {
        isValidate = false;
        $("#dob_error").remove();
        $("#dob").css("border-color", "red");
        $("#dob").after('<span id="dob_error" style="color: red; font-size: 12px;">Date of Birth Field is Required.</span>');
      }
      if (gender === "") {
        isValidate = false;
        $("#gender_error").remove();
        $("#gender").css("border-color", "red");
        $("#gender").after('<span id="gender_error" style="color: red; font-size: 12px;">Gender Field is Required.</span>');
      }
      if (marital_status === "") {
        isValidate = false;
        $("#marital_status_error").remove();
        $("#marital_status").css("border-color", "red");
        $("#marital_status").after('<span id="marital_status_error" style="color: red; font-size: 12px;">Marital Status Field is Required.</span>');
      }
      if (social_category === "") {
        isValidate = false;
        $("#social_category_error").remove();
        $("#social_category").css("border-color", "red");
        $("#social_category").after('<span id="social_category_error" style="color: red; font-size: 12px;">Social Category Field is Required.</span>');

      }
      if (income === "") {
        isValidate = false;
        $("#income_error").remove();
        $("#income").css("border-color", "red");
        $("#income").after('<span id="income_error" style="color: red; font-size: 12px;">Annual Income Field is Required.</span>');

      }


      //error check
      /*if (name != "") {
        $("#name").css("border-color", "green");
        $("#name_error").remove();
      }*/
      if (dob != "") {
        $("#dob").css("border-color", "green");
        $("#dob_error").remove();
      }
      if (gender != "") {
        $("#gender").css("border-color", "green");
        $("#gender_error").remove();
      }
      if (marital_status != "") {
        $("#marital_status").css("border-color", "green");
        $("#marital_status_error").remove();
      }
      if (social_category != "") {
        $("#social_category").css("border-color", "green");
        $("#social_category_error").remove();
      }
      if (income != "") {
        $("#income").css("border-color", "green");
        $("#income_error").remove();
      }

      //Check all the field is not empty
      if (dob != "" && gender != "" && marital_status != "" && social_category != "" && income != "") {
        isValidate = true;
      }

      if (!isValidate) {
        console.log("invalid");
        return;
      }
      else{
        console.log("valid");
      }




      // Now you can do something with these values, like sending them somewhere or displaying them.
      console.log({
        name,
        dob,
        gender,
        marital_status,
        social_category
      });


      $.ajax({
        url: EODB_CONFIG.formSubmitUrl,
        type: "POST",
        dataType: "json",
        data: {
          dob: dob,
          gender: gender,
          marital_status: marital_status,
          social_category: social_category,
          income: income
        },
        success: function(data) {
          console.log("Form submission response:", data);
          handleChatbotResponse(data);
          enabledSendSection();
        },
        error: function(error) {
          console.error("Error:", error);
          handleBackendError(error);
        }
      });

      /*$("#dob").val("");
        $("#gender").val("");
        $("#marital_status").val("");
        $("#social_category").val("");
        $("#income").val("");*/
    });

    /////////////////////////////////For Aadhaar Form Submit///////////////////////////////////////
    



    ////////////////////////////////////////////////////AAdhaar Number Validation using Verhoeff Algorithm ////////////////// 
    // multiplication table d
    var d = [
      [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
      [1, 2, 3, 4, 0, 6, 7, 8, 9, 5],
      [2, 3, 4, 0, 1, 7, 8, 9, 5, 6],
      [3, 4, 0, 1, 2, 8, 9, 5, 6, 7],
      [4, 0, 1, 2, 3, 9, 5, 6, 7, 8],
      [5, 9, 8, 7, 6, 0, 4, 3, 2, 1],
      [6, 5, 9, 8, 7, 1, 0, 4, 3, 2],
      [7, 6, 5, 9, 8, 2, 1, 0, 4, 3],
      [8, 7, 6, 5, 9, 3, 2, 1, 0, 4],
      [9, 8, 7, 6, 5, 4, 3, 2, 1, 0]
    ];

    // permutation table p
    var p = [
      [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
      [1, 5, 7, 6, 2, 8, 3, 0, 9, 4],
      [5, 8, 0, 3, 7, 9, 6, 1, 4, 2],
      [8, 9, 1, 6, 0, 4, 3, 5, 2, 7],
      [9, 4, 5, 3, 1, 2, 6, 8, 7, 0],
      [4, 2, 8, 6, 5, 7, 3, 9, 0, 1],
      [2, 7, 9, 3, 8, 0, 6, 4, 1, 5],
      [7, 0, 4, 6, 9, 1, 3, 2, 5, 8]
    ];

    // inverse table inv
    var inv = [0, 4, 3, 2, 1, 5, 6, 7, 8, 9];

    // converts string or number to an array and inverts it
    function invArray(array) {
      if (Object.prototype.toString.call(array) == "[object Number]") {
        array = String(array);
      }

      if (Object.prototype.toString.call(array) == "[object String]") {
        array = array.split("").map(Number);
      }

      return array.reverse();
    }

    // generates checksum
    function generate(array) {
      var c = 0;
      var invertedArray = invArray(array);

      for (var i = 0; i < invertedArray.length; i++) {
        c = d[c][p[((i + 1) % 8)][invertedArray[i]]];
      }

      return inv[c];
    }

    // validates checksum
    function validate(array) {
      var c = 0;
      var invertedArray = invArray(array);

      for (var i = 0; i < invertedArray.length; i++) {
        c = d[c][p[(i % 8)][invertedArray[i]]];
      }
      return (c === 0);
    }

    // jQuery function to validate Aadhaar number
    $(document).on("keyup", "#aadhaar_number", function(e) {
      e.preventDefault();

      var aadhaar_number = $("#aadhaar_number").val().trim(); // Remove extra spaces

      // Regular Expression for Aadhaar number validation (12 digits)
      var aadhaarRegex = /^\d{12}$/;

      // Remove any previous error message
      $("#aadhaar_error").text("");

      // Check if the number matches the 12-digit format
      if (aadhaar_number.match(aadhaarRegex)) {
        console.log("Valid format for Aadhaar number");

        // Now, check using the Verhoeff algorithm for checksum validation
        if (validate(aadhaar_number)) {
          console.log("Valid Aadhaar number (Checksum validated)");
          // Optionally, visual feedback for valid input
          $("#aadhaar_number").css("border-color", "green");
          $("#aadhaar_number").after('<span id="aadhaar_error" style="color: green; font-size: 12px;">Valid Aadhaar number.</span>');

        } else {
          console.log("Invalid Aadhaar number (Checksum failed)");
          // Visual feedback for invalid input
          $("#aadhaar_number").after('<span id="aadhaar_error" style="color: red; font-size: 12px;">Invalid Aadhaar number (Checksum failed).</span>');
          $("#aadhaar_number").css("border-color", "red");
          //$("#aadhaar_error").text("Invalid Aadhaar number (Checksum failed).");
        }

      } else {
        console.log("Invalid Aadhaar number format");
        // Visual feedback for invalid input
        $("#aadhaar_number").css("border-color", "red");

        // Display error message
        $("#aadhaar_number").after('<span id="aadhaar_error" style="color: red; font-size: 12px;">Invalid Aadhaar number format. It should be exactly 12 digits.</span>');

      }
    });

    ///////////////////////////////End of Aadhaar Number Validation/////////////////////////////

    // Session create automatically when chat box open and select language
    $(document).on("click", "#select_L_B", function() {
      $.ajax({
        url: "<?php echo base_url('super_admin/ChatbotEntryController/sessionCreateEODB'); ?>",
        type: "POST",
        dataType: "json",
        data: {
          type: "session"
        },
        success: function(data) {
          console.log("response=" + data)
          $("#result").html(`<p>Status: ${data.status}</p>`);
        },
        error: function(error) {
          console.error("Error:", error);
          handleBackendError(error);
        }
      });


    });



    $(document).on("click", ".close", function() {

      $.ajax({
        url: "<?php echo base_url('super_admin/ChatbotEntryController/sessionDestroyEODB'); ?>", // Replace with your CodeIgniter route for suggestions
        type: "POST",
        dataType: "json",
        /*contentType: "application/json",*/
        data: {
          type: "session"

        },
        success: function(data) {
          console.log("response=" + data)
          $("#result").html(`<p>Status: ${data.status}</p>`);
        },
        error: function(error) {
          console.error("Error:", error);
          handleBackendError(error);
        }
      });


    });



    // Attach click event handler to all buttons with class 'option_btn'
    $(document).on("click", ".option_btn", function() {
      // Show loader
      $("#c_loader").show();

      // Get the value of the clicked button
      var buttonValue = $(this).val();
      var buttonText = $(this).text();
      console.log("Button clicked - Value:", buttonValue, "Text:", buttonText);
      console.log("Current step:", currentStep);
      var ques = buttonText;

      // Skip keyword checks for option buttons - they should follow the normal flow
      selectPythonApiOption(buttonValue || ques);
    });





  });

  // Session destroy automatically on page load
  $(document).ready(function() {
    $.ajax({
      url: "<?php echo base_url('super_admin/ChatbotEntryController/sessionDestroyEODB'); ?>",
      type: "POST",
      dataType: "json",
      data: {
        type: "session"
      },
      success: function(data) {
        console.log("response=" + data)
        $("#result").html(`<p>Status: ${data.status}</p>`);
      },
      error: function(error) {
        console.error("Error:", error);
        handleBackendError(error);
      }
    });



  });

  // Function to redirect to specific service
  function redirectToService(serviceName) {
    // Clear current session and start new service-specific session
    addMessageToChatbox(`Redirecting to ${serviceName}...`, "visitor");

    // Start licence flow and set the service
    $.ajax({
      url: EODB_CONFIG.pythonApiUrl,
      type: "POST",
      dataType: "json",
      contentType: "application/json",
      data: JSON.stringify({
        session_id: currentSessionId,
        step: 3,
        response_type: "options",
        collection_name: EODB_CONFIG.collectionName,
        user_response: {
          caption: "selected_service",
          value: serviceName
        }
      }),
      success: function(data) {
        console.log("Service redirect response:", JSON.stringify(data));
        handlePythonApiResponse(data);
      },
      error: function(error) {
        console.error("Error:", error);
        handleBackendError(error);
      }
    });
  }

  // Function to send messages (used by navigation buttons)
  function sendMessage(message) {
    // Check for special keywords
    if (checkSpecialKeywords(message)) {
      return;
    }

    addMessageToChatbox(message, "visitor");

    // Handle navigation messages
    if (message === "Previous Menu" || message === "Main Menu") {
      // Use Python API for navigation
      $.ajax({
        url: EODB_CONFIG.pythonApiUrl,
        type: "POST",
        dataType: "json",
        contentType: "application/json",
        data: JSON.stringify({
          session_id: currentSessionId,
          user_input: message,
          step: currentStep || 1,
          response_type: "options",
          collection_name: EODB_CONFIG.collectionName
        }),
        success: function(data) {
          console.log("Navigation response:", JSON.stringify(data));
          handlePythonApiResponse(data);
        },
        error: function(error) {
          console.error("Error:", error);
          handleBackendError(error);
        }
      });
    } else {
      // For other messages, treat as general queries
      $.ajax({
        url: EODB_CONFIG.pythonApiUrl,
        type: "POST",
        dataType: "json",
        contentType: "application/json",
        data: JSON.stringify({
          session_id: currentSessionId,
          user_input: message,
          step: currentStep || 1,
          response_type: "text",
          collection_name: EODB_CONFIG.collectionName
        }),
        success: function(data) {
          console.log("SendMessage response:", JSON.stringify(data));
          handlePythonApiResponse(data);
        },
        error: function(error) {
          console.error("Error:", error);
          handleBackendError(error);
        }
      });
    }
  }

  // Function to ask about a specific service (shows service query buttons)
  function askAboutService(serviceName) {
    addMessageToChatbox(`Ask about ${serviceName}`, "visitor");

    const requestData = {
      session_id: currentSessionId,
      step: 4,
      response_type: "options",
      collection_name: EODB_CONFIG.collectionName,
      user_response: {
        caption: "selected_service",
        value: serviceName
      }
    };

    console.log("=== ASK ABOUT SERVICE DEBUG ===");
    console.log("Service Name:", serviceName);
    console.log("Session ID:", currentSessionId);
    console.log("Request Data:", JSON.stringify(requestData, null, 2));
    console.log("=== END ASK ABOUT SERVICE DEBUG ===");

    $.ajax({
      url: EODB_CONFIG.pythonApiUrl,
      type: "POST",
      dataType: "json",
      contentType: "application/json",
      data: JSON.stringify(requestData),
      success: function(data) {
        console.log("Ask about service response:", JSON.stringify(data));
        if (data.step) {
          currentStep = data.step;
        }
        handlePythonApiResponse(data);
      },
      error: function(error) {
        console.error("Error:", error);
        handleBackendError(error);
      }
    });
  }

  // Function to redirect to a specific service
  function redirectToService(serviceName) {
    addMessageToChatbox(`Redirecting to ${serviceName}...`, "visitor");

    // Start licence flow and set the service
    $.ajax({
      url: EODB_CONFIG.pythonApiUrl,
      type: "POST",
      dataType: "json",
      contentType: "application/json",
      data: JSON.stringify({
        session_id: currentSessionId,
        step: 3,
        response_type: "options",
        collection_name: EODB_CONFIG.collectionName,
        user_response: {
          caption: "selected_service",
          value: serviceName
        }
      }),
      success: function(data) {
        console.log("Service redirect response:", JSON.stringify(data));
        if (data.step) {
          currentStep = data.step;
        }
        handlePythonApiResponse(data);
      },
      error: function(error) {
        console.error("Error:", error);
        handleBackendError(error);
      }
    });
  }

</script>





