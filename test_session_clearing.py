#!/usr/bin/env python3
"""
Test script to verify session clearing functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import (
    store_selected_service, 
    store_session_data, 
    get_selected_service,
    get_session_data,
    clear_all_session_data,
    session_data_store
)

def test_session_clearing():
    """Test that session clearing works properly"""
    test_session_id = "test_session_123"
    
    print("=== Testing Session Clearing ===")
    
    # 1. Set up a service in both storage mechanisms
    print("1. Setting up service in both storages...")
    store_selected_service(test_session_id, "Test Service")
    store_session_data(test_session_id, {"selected_service": "Test Service"})
    
    # 2. Verify service is stored
    print("2. Verifying service is stored...")
    service = get_selected_service(test_session_id)
    print(f"   Selected service: '{service}'")
    
    # 3. Clear all session data
    print("3. Clearing all session data...")
    clear_all_session_data(test_session_id)
    
    # 4. Verify service is cleared
    print("4. Verifying service is cleared...")
    service_after_clear = get_selected_service(test_session_id)
    print(f"   Selected service after clear: '{service_after_clear}'")
    
    # 5. Check in-memory storage directly
    print("5. Checking in-memory storage...")
    in_memory = get_session_data(test_session_id, "selected_service")
    print(f"   In-memory service: '{in_memory}'")
    
    # 6. Test result
    if service_after_clear is None and (in_memory is None or in_memory == ""):
        print("✅ SUCCESS: Session clearing works correctly!")
        return True
    else:
        print("❌ FAILURE: Session clearing did not work properly!")
        return False

if __name__ == "__main__":
    test_session_clearing()
